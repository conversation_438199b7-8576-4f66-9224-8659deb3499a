# -*- coding: utf-8 -*-
"""
OCR引擎管理模块
负责EasyOCR和PaddleOCR引擎的初始化和管理
"""

import logging
import traceback
import warnings
from typing import Dict, Any
import easyocr
import paddleocr

class OCREngineManager:
    """OCR引擎管理器"""
    
    def __init__(self, use_gpu: bool = True, debug_mode: bool = False):
        """
        初始化OCR引擎管理器
        
        Args:
            use_gpu: 是否使用GPU加速
            debug_mode: 是否启用调试模式
        """
        self.logger = logging.getLogger(__name__)
        self.use_gpu = use_gpu
        self.debug_mode = debug_mode
        self.reader = None
        self.paddle_reader = None
        self.ocr_engines = {'easyocr': False, 'paddleocr': False}
        self.error_count = 0
        self.max_errors = 10
        
        # 初始化引擎
        self._init_ocr_engines()
    
    def _check_gpu_availability(self) -> bool:
        """
        检查GPU是否可用
        
        Returns:
            是否有可用的GPU
        """
        try:
            import torch
            gpu_available = torch.cuda.is_available() and torch.cuda.device_count() > 0
            if gpu_available:
                device_name = torch.cuda.get_device_name(0)
                self.logger.info(f"检测到GPU设备: {device_name}")
            else:
                self.logger.info("未检测到可用的GPU设备，将使用CPU模式")
            return gpu_available
        except ImportError:
            self.logger.info("PyTorch未安装或不支持CUDA，将使用CPU模式")
            return False
        except Exception as e:
            self.logger.warning(f"GPU检测过程中出现错误: {str(e)}，将使用CPU模式")
            return False

    def _init_ocr_engines(self):
        """初始化所有OCR引擎"""
        self.logger.info("开始初始化OCR引擎...")
        
        # 智能GPU检测
        gpu_available = self._check_gpu_availability()
        actual_use_gpu = self.use_gpu and gpu_available
        
        if self.use_gpu and not gpu_available:
            self.logger.info("用户指定使用GPU但系统无可用GPU，自动切换到CPU模式")
        
        # 初始化EasyOCR
        self._init_easyocr(actual_use_gpu)
        
        # 初始化PaddleOCR
        self._init_paddleocr(actual_use_gpu)
        
        # 检查是否有可用引擎
        available_engines = [engine for engine, status in self.ocr_engines.items() if status]
        if not available_engines:
            error_msg = "没有可用的OCR引擎！请检查安装和配置。"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg)
        else:
            self.logger.info(f"可用的OCR引擎: {', '.join(available_engines)}")
    
    def _init_easyocr(self, use_gpu: bool):
        """
        初始化EasyOCR引擎
        
        Args:
            use_gpu: 是否使用GPU
        """
        try:
            if self.debug_mode:
                self.logger.debug(f"初始化EasyOCR引擎 (GPU: {use_gpu})...")
            
            # 抑制EasyOCR的警告信息
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                self.reader = easyocr.Reader(['ch_sim', 'en'], gpu=use_gpu, verbose=False)
            
            self.ocr_engines['easyocr'] = True
            self.logger.info(f"EasyOCR引擎初始化成功 ({'GPU' if use_gpu else 'CPU'}模式)")
            
        except Exception as e:
            self.logger.error(f"EasyOCR引擎初始化失败: {str(e)}")
            if self.debug_mode:
                self.logger.debug(f"EasyOCR错误详情: {traceback.format_exc()}")
            
            # 如果GPU模式失败，尝试CPU模式
            if use_gpu:
                try:
                    self.logger.info("GPU模式失败，尝试CPU模式...")
                    with warnings.catch_warnings():
                        warnings.simplefilter("ignore")
                        self.reader = easyocr.Reader(['ch_sim', 'en'], gpu=False, verbose=False)
                    
                    self.ocr_engines['easyocr'] = True
                    self.logger.info("EasyOCR引擎初始化成功 (CPU回退模式)")
                    
                except Exception as e2:
                    self.logger.error(f"EasyOCR CPU模式初始化也失败: {str(e2)}")
                    if self.debug_mode:
                        self.logger.debug(f"EasyOCR CPU模式错误详情: {traceback.format_exc()}")
    
    def _init_paddleocr(self, use_gpu: bool):
        """
        初始化PaddleOCR引擎
        
        Args:
            use_gpu: 是否使用GPU
        """
        try:
            if self.debug_mode:
                self.logger.debug(f"初始化PaddleOCR引擎 (GPU: {use_gpu})...")
            
            # 配置PaddleOCR参数
            paddle_config = {
                'use_angle_cls': True,
                'lang': 'ch',
                'show_log': False
            }
            
            # 如果不使用GPU，明确设置use_gpu=False
            if not use_gpu:
                paddle_config['use_gpu'] = False
            
            self.paddle_reader = paddleocr.PaddleOCR(**paddle_config)
            self.ocr_engines['paddleocr'] = True
            self.logger.info(f"PaddleOCR引擎初始化成功 ({'GPU' if use_gpu else 'CPU'}模式)")
            
        except Exception as e:
            self.logger.error(f"PaddleOCR引擎初始化失败: {str(e)}")
            if self.debug_mode:
                self.logger.debug(f"PaddleOCR错误详情: {traceback.format_exc()}")
            
            # 如果GPU模式失败，尝试CPU模式
            if use_gpu:
                try:
                    self.logger.info("PaddleOCR GPU模式失败，尝试CPU模式...")
                    paddle_config = {
                        'use_angle_cls': True,
                        'lang': 'ch',
                        'show_log': False,
                        'use_gpu': False
                    }
                    self.paddle_reader = paddleocr.PaddleOCR(**paddle_config)
                    self.ocr_engines['paddleocr'] = True
                    self.logger.info("PaddleOCR引擎初始化成功 (CPU回退模式)")
                    
                except Exception as e2:
                    self.logger.error(f"PaddleOCR CPU模式初始化也失败: {str(e2)}")
                    if self.debug_mode:
                        self.logger.debug(f"PaddleOCR CPU模式错误详情: {traceback.format_exc()}")
    
    def get_engine_status(self) -> Dict[str, Any]:
        """
        获取OCR引擎状态
        
        Returns:
            引擎状态信息
        """
        return {
            'available_engines': self.ocr_engines,
            'error_count': self.error_count,
            'max_errors': self.max_errors,
            'debug_mode': self.debug_mode
        }
    
    def reset_error_count(self):
        """重置错误计数"""
        self.error_count = 0
        self.logger.info("错误计数已重置")
    
    def is_easyocr_available(self) -> bool:
        """检查EasyOCR是否可用"""
        return self.ocr_engines['easyocr'] and self.reader is not None
    
    def is_paddleocr_available(self) -> bool:
        """检查PaddleOCR是否可用"""
        return self.ocr_engines['paddleocr'] and self.paddle_reader is not None
    
    def get_easyocr(self):
        """获取EasyOCR实例"""
        if self.is_easyocr_available():
            return self.reader
        return None
    
    def get_paddleocr(self):
        """获取PaddleOCR实例"""
        if self.is_paddleocr_available():
            return self.paddle_reader
        return None

    def multi_engine_ocr(self, image) -> Dict[str, Any]:
        """
        使用多个OCR引擎进行识别

        Args:
            image: 要识别的图像

        Returns:
            各引擎的识别结果
        """
        results = {}

        # 尝试EasyOCR
        if self.is_easyocr_available():
            try:
                if self.debug_mode:
                    self.logger.debug("使用EasyOCR进行识别...")

                easyocr_results = self.reader.readtext(image)
                results['easyocr'] = easyocr_results

                if self.debug_mode:
                    self.logger.debug(f"EasyOCR识别结果: {easyocr_results}")

            except Exception as e:
                self.logger.error(f"EasyOCR识别失败: {str(e)}")
                results['easyocr'] = []

        # 尝试PaddleOCR
        if self.is_paddleocr_available():
            try:
                if self.debug_mode:
                    self.logger.debug("使用PaddleOCR进行识别...")

                paddleocr_results = self.paddle_reader.ocr(image, cls=True)
                results['paddleocr'] = paddleocr_results

                if self.debug_mode:
                    self.logger.debug(f"PaddleOCR识别结果: {paddleocr_results}")

            except Exception as e:
                self.logger.error(f"PaddleOCR识别失败: {str(e)}")
                results['paddleocr'] = []

        return results