# -*- coding: utf-8 -*-
"""
股票筛选器主程序
基于指南针软件的股票多空资金数据筛选工具
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import logging
import os
import sys
from typing import List, Dict, Any, Optional
import queue
import traceback

from config import GUI_CONFIG, APP_CONFIG, COMPASS_SOFTWARE, update_ocr_region_config
from data_processor import DataProcessor
from compass_automator import CompassAutomator
from region_selector import RegionSelector
from ocr_manager_optimized import get_global_ocr_manager

class StockScreenerGUI:
    """股票筛选器GUI主类"""
    
    def __init__(self):
        """初始化GUI"""
        self.root = tk.Tk()
        self.setup_logging()
        self.setup_window()
        self.setup_variables()
        self.setup_widgets()
        self.setup_layout()
        
        # 初始化组件
        self.data_processor = DataProcessor()
        self.compass_automator = None
        self.ocr_manager = get_global_ocr_manager()
        self.is_processing = False
        self.current_excel_path = ""
        self.selected_region = None  # 存储选择的区域坐标
        
        # 消息队列用于线程间通信
        self.message_queue = queue.Queue()
        self.check_message_queue()
        
        # 日志处理器
        self.log_handler = GUILogHandler(self.log_text)
        self.logger.addHandler(self.log_handler)
        
        # 读取配置文件中的区域信息并初始化OCR
        self.load_saved_region()
        self.init_ocr_early()
        
        # 更新界面状态（在所有组件都初始化完成后）
        self.update_ui_after_init()
    
    def load_saved_region(self):
        """读取配置文件中保存的区域信息"""
        try:
            if COMPASS_SOFTWARE.get('ocr_region'):
                region = COMPASS_SOFTWARE['ocr_region']
                x, y, width, height = region['x'], region['y'], region['width'], region['height']
                
                # 检查区域配置是否有效（非默认值）
                if not (x == 100 and y == 200 and width == 300 and height == 100):
                    self.selected_region = (x, y, width, height)
                    self.logger.info(f"加载已保存的区域配置: ({x}, {y}) 尺寸: {width}×{height}")
                    return True
                    
        except Exception as e:
            self.logger.error(f"读取区域配置失败: {str(e)}")
            
        return False
    
    def init_ocr_early(self):
        """程序启动时提前初始化OCR模块"""
        try:
            # 从配置获取OCR设置
            ocr_config = getattr(APP_CONFIG, 'ocr_settings', {})
            use_gpu = ocr_config.get('use_gpu', True)
            debug_mode = ocr_config.get('debug_mode', False)
            save_debug_images = ocr_config.get('save_debug_images', False)
            
            # 使用OCR管理器初始化
            if self.ocr_manager.initialize_ocr(use_gpu, debug_mode, save_debug_images):
                status = self.ocr_manager.get_engine_status()
                available_engines = [name for name, available in status['available_engines'].items() if available]
                self.logger.info(f"OCR模块提前初始化成功，可用引擎: {', '.join(available_engines)}")
                return True
            else:
                self.logger.error("OCR模块初始化失败：无可用引擎")
                return False
                
        except Exception as e:
            self.logger.error(f"OCR模块提前初始化失败: {str(e)}")
            return False
    
    def update_ui_after_init(self):
        """程序初始化后更新界面状态"""
        # 更新OCR状态显示
        if self.ocr_manager.is_initialized():
            self._update_ocr_status()
        
        # 如果有已保存的区域，更新界面显示
        if self.selected_region:
            x, y, width, height = self.selected_region
            region_text = f"区域: ({x}, {y}) 尺寸: {width}×{height}"
            self.region_info_var.set(region_text)
            
            # 启用测试OCR按钮
            self.test_ocr_btn.config(state=tk.NORMAL)
            self.test_fund_ocr_btn.config(state=tk.NORMAL)
            
            self.logger.info(f"界面已更新，显示保存的区域配置: {region_text}")
        else:
            self.region_info_var.set("未选择区域")
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=getattr(logging, APP_CONFIG['log_level']),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_window(self):
        """设置主窗口"""
        config = GUI_CONFIG['main_window']
        self.root.title(config['title'])
        self.root.geometry(f"{config['width']}x{config['height']}")
        self.root.minsize(config['min_width'], config['min_height'])
        
        # 设置窗口居中
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (config['width'] // 2)
        y = (self.root.winfo_screenheight() // 2) - (config['height'] // 2)
        self.root.geometry(f"{config['width']}x{config['height']}+{x}+{y}")
        
    def setup_variables(self):
        """设置变量"""
        self.excel_path_var = tk.StringVar()
        self.status_var = tk.StringVar(value=GUI_CONFIG['status_bar']['ready_message'])
        self.progress_var = tk.DoubleVar()
        self.region_info_var = tk.StringVar()
        
    def setup_widgets(self):
        """设置控件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 文件选择框架
        file_frame = ttk.LabelFrame(main_frame, text="Excel文件选择", padding="10")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Excel文件路径
        ttk.Label(file_frame, text="Excel文件:").grid(row=0, column=0, sticky=tk.W)
        self.excel_entry = ttk.Entry(file_frame, textvariable=self.excel_path_var, width=50)
        self.excel_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0))
        
        # 文件选择按钮
        ttk.Button(file_frame, text="选择文件", command=self.select_excel_file).grid(
            row=0, column=2, padx=(10, 0)
        )
        
        # 区域选择按钮框架
        region_frame = ttk.LabelFrame(main_frame, text="OCR区域设置", padding="10")
        region_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 区域选择按钮
        ttk.Button(region_frame, text="选择屏幕区域", command=self.select_screen_region).grid(
            row=0, column=0, padx=(0, 10)
        )
        
        # 区域信息显示
        self.region_info_var = tk.StringVar(value="未选择区域")
        self.region_label = ttk.Label(region_frame, textvariable=self.region_info_var)
        self.region_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # 测试OCR按钮
        self.test_ocr_btn = ttk.Button(region_frame, text="测试OCR识别", 
                                      command=self.test_ocr_recognition, state=tk.DISABLED)
        self.test_ocr_btn.grid(row=0, column=2, padx=(10, 0))
        
        # 测试多空资金OCR按钮
        self.test_fund_ocr_btn = ttk.Button(region_frame, text="测试多空资金OCR", 
                                           command=self.test_fund_ocr_recognition, state=tk.DISABLED)
        self.test_fund_ocr_btn.grid(row=1, column=2, padx=(10, 0), pady=(5, 0))
        
        # OCR诊断按钮
        self.ocr_diagnose_btn = ttk.Button(region_frame, text="OCR诊断", 
                                          command=self.run_ocr_diagnostics)
        self.ocr_diagnose_btn.grid(row=0, column=3, padx=(10, 0))
        
        # OCR引擎状态显示
        self.ocr_status_label = ttk.Label(region_frame, text="OCR状态: 未初始化", foreground="orange")
        self.ocr_status_label.grid(row=1, column=0, columnspan=4, sticky="w", pady=(5, 0))
        
        # 控制按钮框架
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 开始分析按钮
        self.start_btn = ttk.Button(control_frame, text="开始分析", command=self.start_analysis)
        self.start_btn.grid(row=0, column=0, padx=(0, 10))
        
        # 停止分析按钮
        self.stop_btn = ttk.Button(control_frame, text="停止分析", command=self.stop_analysis, state=tk.DISABLED)
        self.stop_btn.grid(row=0, column=1, padx=(0, 10))
        
        # 测试连接按钮
        ttk.Button(control_frame, text="测试连接", command=self.test_connection).grid(
            row=0, column=2, padx=(0, 10)
        )
        
        # 保存结果按钮
        self.save_btn = ttk.Button(control_frame, text="保存结果", command=self.save_results, state=tk.DISABLED)
        self.save_btn.grid(row=0, column=3)
        
        # 进度条
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 结果表格框架
        table_frame = ttk.LabelFrame(main_frame, text="筛选结果", padding="10")
        table_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 结果表格
        self.setup_result_table(table_frame)
        
        # 日志框架
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="10")
        log_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 状态栏
        self.status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
    def setup_result_table(self, parent):
        """设置结果表格"""
        # 创建表格
        columns = GUI_CONFIG['table']['columns']
        self.result_tree = ttk.Treeview(parent, columns=columns, show='headings', height=10)
        
        # 设置列标题和宽度
        for i, col in enumerate(columns):
            self.result_tree.heading(col, text=col)
            width = GUI_CONFIG['table']['column_widths'][i]
            self.result_tree.column(col, width=width, anchor=tk.CENTER)
        
        # 添加滚动条
        scrollbar_v = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.result_tree.yview)
        scrollbar_h = ttk.Scrollbar(parent, orient=tk.HORIZONTAL, command=self.result_tree.xview)
        self.result_tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
        
        # 布局
        self.result_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar_v.grid(row=0, column=1, sticky=(tk.N, tk.S))
        scrollbar_h.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # 配置权重
        parent.grid_rowconfigure(0, weight=1)
        parent.grid_columnconfigure(0, weight=1)
        
    def setup_layout(self):
        """设置布局权重"""
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)
        
        # 主框架布局
        main_frame = self.root.grid_slaves()[0]
        main_frame.grid_rowconfigure(4, weight=1)
        main_frame.grid_rowconfigure(5, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)
        
    def select_excel_file(self):
        """选择Excel文件"""
        filetypes = [
            ("Excel files", "*.xlsx *.xls"),
            ("Excel 2007+", "*.xlsx"),
            ("Excel 97-2003", "*.xls"),
            ("All files", "*.*")
        ]
        
        filename = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=filetypes
        )
        
        if filename:
            self.excel_path_var.set(filename)
            self.current_excel_path = filename
            self.logger.info(f"已选择Excel文件: {filename}")
    
    def select_screen_region(self):
        """选择屏幕区域"""
        try:
            self.logger.info("开始选择屏幕区域...")
            
            # 创建区域选择器并设置回调
            selector = RegionSelector(callback=self.on_region_selected)
            
            # 开始选择
            region = selector.start_selection()
            
            if region:
                self.selected_region = region
                self.logger.info(f"区域选择完成: {region}")
            else:
                self.logger.info("用户取消了区域选择")
                
        except Exception as e:
            self.logger.error(f"区域选择失败: {str(e)}")
            messagebox.showerror("错误", f"区域选择失败: {str(e)}")
    
    def on_region_selected(self, x: int, y: int, width: int, height: int):
        """区域选择完成回调"""
        self.selected_region = (x, y, width, height)
        
        # 更新区域信息显示
        region_text = f"区域: ({x}, {y}) 尺寸: {width}×{height}"
        self.region_info_var.set(region_text)
        
        # 启用测试OCR按钮
        self.test_ocr_btn.config(state=tk.NORMAL)
        self.test_fund_ocr_btn.config(state=tk.NORMAL)
        
        self.logger.info(f"选择区域: {region_text}")
        
        # 保存区域配置到配置文件
        try:
            if update_ocr_region_config(x, y, width, height):
                self.logger.info("区域配置已保存到配置文件")
            else:
                self.logger.warning("保存区域配置到配置文件失败")
        except Exception as e:
            self.logger.error(f"保存区域配置失败: {str(e)}")
        
        # 由于OCR已在程序启动时初始化，这里不再需要初始化
        # 只需要更新OCR状态显示
        if self.ocr_manager.is_initialized():
            self._update_ocr_status()
    
    def run_ocr_diagnostics(self):
        """运行OCR诊断"""
        try:
            self.logger.info("开始OCR诊断...")
            
            def diagnose_thread():
                try:
                    from ocr_diagnostics import OCRDiagnostics
                    
                    # 运行诊断
                    diagnostics = OCRDiagnostics()
                    report = diagnostics.run_full_diagnostics()
                    
                    # 通过消息队列返回结果
                    self.message_queue.put(("ocr_diagnosis_result", report))
                    
                except Exception as e:
                    self.message_queue.put(("ocr_diagnosis_error", str(e)))
            
            threading.Thread(target=diagnose_thread, daemon=True).start()
            
        except Exception as e:
            self.logger.error(f"OCR诊断失败: {str(e)}")
            messagebox.showerror("错误", f"OCR诊断失败: {str(e)}")
    
    def test_fund_ocr_recognition(self):
        """测试多空资金OCR识别"""
        if not self.selected_region:
            messagebox.showwarning("警告", "请先选择屏幕区域")
            return
        
        if not self.ocr_manager.is_initialized():
            messagebox.showerror("错误", "OCR引擎未初始化")
            return
        
        try:
            self.logger.info("开始测试多空资金OCR识别...")
            
            # 检查OCR引擎状态
            status = self.ocr_manager.get_engine_status()
            available_engines = [name for name, available in status['available_engines'].items() if available]
            
            if not available_engines:
                messagebox.showerror("错误", "没有可用的OCR引擎，请运行OCR诊断检查问题")
                return
            
            # 禁用测试按钮，防止重复点击
            self.test_fund_ocr_btn.config(state=tk.DISABLED)
            self.ocr_status_label.config(text="OCR状态: 测试多空资金识别中...", foreground="blue")
            
            # 在新线程中执行OCR测试以避免阻塞GUI
            def test_fund_thread():
                try:
                    x, y, width, height = self.selected_region
                    
                    # 重置错误计数
                    self.ocr_manager.reset_error_count()
                    
                    # 获取OCR引擎并测试
                    ocr_engine = self.ocr_manager.get_optimized_ocr_engine()
                    if ocr_engine:
                        if hasattr(ocr_engine, 'test_fund_data_ocr_recognition_optimized'):
                            result = ocr_engine.test_fund_data_ocr_recognition_optimized(x, y, width, height)
                        else:
                            result = ocr_engine.test_fund_data_ocr_recognition(x, y, width, height)
                        # 通过消息队列返回结果
                        self.message_queue.put(("fund_ocr_test_result", result))
                    else:
                        self.message_queue.put(("fund_ocr_test_error", "无法获取OCR引擎"))
                    
                except Exception as e:
                    self.message_queue.put(("fund_ocr_test_error", str(e)))
                finally:
                    # 重新启用按钮
                    self.message_queue.put(("fund_ocr_test_complete", None))
            
            threading.Thread(target=test_fund_thread, daemon=True).start()
            
        except Exception as e:
            self.logger.error(f"多空资金OCR测试失败: {str(e)}")
            messagebox.showerror("错误", f"多空资金OCR测试失败: {str(e)}")
            self.test_fund_ocr_btn.config(state=tk.NORMAL)
            self._update_ocr_status()
    
    def test_ocr_recognition(self):
        """测试OCR识别"""
        if not self.selected_region:
            messagebox.showwarning("警告", "请先选择屏幕区域")
            return
        
        if not self.ocr_manager.is_initialized():
            messagebox.showerror("错误", "OCR引擎未初始化")
            return
        
        try:
            self.logger.info("开始测试OCR识别...")
            
            # 检查OCR引擎状态
            status = self.ocr_manager.get_engine_status()
            available_engines = [name for name, available in status['available_engines'].items() if available]
            
            if not available_engines:
                messagebox.showerror("错误", "没有可用的OCR引擎，请运行OCR诊断检查问题")
                return
            
            # 禁用测试按钮，防止重复点击
            self.test_ocr_btn.config(state=tk.DISABLED)
            self.ocr_status_label.config(text="OCR状态: 测试中...", foreground="blue")
            
            # 在新线程中执行OCR测试以避免阻塞GUI
            def test_thread():
                try:
                    x, y, width, height = self.selected_region
                    
                    # 重置错误计数
                    self.ocr_manager.reset_error_count()
                    
                    # 获取OCR引擎并测试
                    fund_ocr = self.ocr_manager.get_ocr_engine()
                    if fund_ocr:
                        result = fund_ocr.test_raw_ocr_recognition(x, y, width, height)
                        # 通过消息队列返回结果
                        self.message_queue.put(("ocr_raw_test_result", result))
                    else:
                        self.message_queue.put(("ocr_test_error", "无法获取OCR引擎"))
                    
                except Exception as e:
                    self.message_queue.put(("ocr_test_error", str(e)))
                finally:
                    # 重新启用按钮
                    self.message_queue.put(("ocr_test_complete", None))
            
            threading.Thread(target=test_thread, daemon=True).start()
            
        except Exception as e:
            self.logger.error(f"OCR测试失败: {str(e)}")
            messagebox.showerror("错误", f"OCR测试失败: {str(e)}")
            self.test_ocr_btn.config(state=tk.NORMAL)
            self._update_ocr_status()
    
    def _update_ocr_status(self):
        """更新OCR状态显示"""
        if self.ocr_manager.is_initialized():
            status = self.ocr_manager.get_engine_status()
            available_engines = [name for name, available in status['available_engines'].items() if available]
            error_count = status.get('error_count', 0)
            
            if available_engines:
                if error_count > 0:
                    status_text = f"OCR状态: 运行中 ({', '.join(available_engines)}) - 错误:{error_count}"
                    status_color = "orange"
                else:
                    status_text = f"OCR状态: 就绪 ({', '.join(available_engines)})"
                    status_color = "green"
            else:
                status_text = "OCR状态: 无可用引擎"
                status_color = "red"
                
            self.ocr_status_label.config(text=status_text, foreground=status_color)
        else:
            self.ocr_status_label.config(text="OCR状态: 未初始化", foreground="orange")
            
            
    def test_connection(self):
        """测试指南针连接"""
        def test_thread():
            try:
                self.message_queue.put(("status", "正在测试连接..."))
                self.message_queue.put(("progress", 50))
                
                automator = CompassAutomator()
                success = automator.start_compass_software()
                
                if success:
                    self.message_queue.put(("log", "指南针连接测试成功"))
                    self.message_queue.put(("status", "连接测试成功"))
                    automator.close_compass_software()
                else:
                    self.message_queue.put(("log", "指南针连接测试失败"))
                    self.message_queue.put(("status", "连接测试失败"))
                    
            except Exception as e:
                self.message_queue.put(("log", f"连接测试错误: {str(e)}"))
                self.message_queue.put(("status", "连接测试错误"))
            finally:
                self.message_queue.put(("progress", 0))
                
        threading.Thread(target=test_thread, daemon=True).start()
        
    def start_analysis(self):
        """开始分析"""
        if not self.current_excel_path:
            messagebox.showerror("错误", "请先选择Excel文件")
            return
            
        if not os.path.exists(self.current_excel_path):
            messagebox.showerror("错误", "Excel文件不存在")
            return
            
        self.is_processing = True
        self.start_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
        self.save_btn.config(state=tk.DISABLED)
        
        # 清空结果表格
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)
            
        # 启动分析线程
        analysis_thread = threading.Thread(target=self.analysis_worker, daemon=True)
        analysis_thread.start()
        
    def analysis_worker(self):
        """分析工作线程"""
        try:
            # 读取Excel文件
            self.message_queue.put(("status", "正在读取Excel文件..."))
            self.message_queue.put(("log", f"开始读取Excel文件: {self.current_excel_path}"))
            
            stock_codes = self.data_processor.load_excel_file(self.current_excel_path)
            total_stocks = len(stock_codes)
            
            self.message_queue.put(("log", f"读取到 {total_stocks} 个股票代码"))
            
            # 初始化指南针自动化
            self.message_queue.put(("status", "正在连接指南针软件..."))
            self.compass_automator = CompassAutomator()
            
            if not self.compass_automator.start_compass_software():
                self.message_queue.put(("log", "无法连接到指南针软件"))
                self.message_queue.put(("status", "连接指南针软件失败"))
                return
                
            # 分析每只股票
            results = []
            for i, stock_code in enumerate(stock_codes):
                if not self.is_processing:
                    break
                    
                progress = (i + 1) / total_stocks * 100
                self.message_queue.put(("progress", progress))
                self.message_queue.put(("status", f"正在分析 {stock_code} ({i+1}/{total_stocks})"))
                
                # 分析股票
                result = self.compass_automator.analyze_single_stock(stock_code)
                results.append(result)
                
                # 更新表格
                self.message_queue.put(("result", result))
                
            # 筛选结果
            self.message_queue.put(("status", "正在筛选结果..."))
            filtered_results = self.data_processor.filter_stocks(results)
            
            # 显示统计信息
            stats = self.data_processor.get_statistics()
            self.message_queue.put(("log", f"筛选完成: {stats['filtered_stocks']}/{stats['total_stocks']} 只股票符合条件"))
            
            self.message_queue.put(("status", "分析完成"))
            self.message_queue.put(("enable_save", True))
            
        except Exception as e:
            self.message_queue.put(("log", f"分析过程中发生错误: {str(e)}"))
            self.message_queue.put(("log", traceback.format_exc()))
            self.message_queue.put(("status", "分析失败"))
        finally:
            self.is_processing = False
            self.message_queue.put(("analysis_complete", None))
            if self.compass_automator:
                self.compass_automator.close_compass_software()
                
    def stop_analysis(self):
        """停止分析"""
        self.is_processing = False
        self.message_queue.put(("status", "正在停止分析..."))
        self.message_queue.put(("log", "用户请求停止分析"))
        
    def save_results(self):
        """保存结果"""
        if not self.data_processor.filtered_stocks:
            messagebox.showwarning("警告", "没有可保存的结果")
            return
            
        filetypes = [
            ("Excel files", "*.xlsx"),
            ("All files", "*.*")
        ]
        
        filename = filedialog.asksaveasfilename(
            title="保存筛选结果",
            defaultextension=".xlsx",
            filetypes=filetypes
        )
        
        if filename:
            try:
                success = self.data_processor.save_results_to_excel(filename, self.data_processor.filtered_stocks)
                if success:
                    messagebox.showinfo("成功", f"结果已保存到: {filename}")
                    self.logger.info(f"结果已保存到: {filename}")
                else:
                    messagebox.showerror("错误", "保存失败")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")
                self.logger.error(f"保存失败: {str(e)}")
                
    def check_message_queue(self):
        """检查消息队列"""
        try:
            while True:
                message_type, data = self.message_queue.get_nowait()
                
                if message_type == "status":
                    self.status_var.set(data)
                elif message_type == "progress":
                    self.progress_var.set(data)
                elif message_type == "log":
                    self.logger.info(data)
                elif message_type == "result":
                    self.add_result_to_table(data)
                elif message_type == "enable_save":
                    self.save_btn.config(state=tk.NORMAL)
                elif message_type == "analysis_complete":
                    self.start_btn.config(state=tk.NORMAL)
                    self.stop_btn.config(state=tk.DISABLED)
                    self.progress_var.set(0)
                elif message_type == "ocr_test_result":
                    self._handle_ocr_test_result(data)
                elif message_type == "ocr_raw_test_result":
                    self._handle_ocr_raw_test_result(data)
                elif message_type == "ocr_test_error":
                    self._handle_ocr_test_error(data)
                elif message_type == "ocr_test_complete":
                    self._handle_ocr_test_complete()
                elif message_type == "ocr_diagnosis_result":
                    self._handle_ocr_diagnosis_result(data)
                elif message_type == "ocr_diagnosis_error":
                    self._handle_ocr_diagnosis_error(data)
                elif message_type == "fund_ocr_test_result":
                    self._handle_fund_ocr_test_result(data)
                elif message_type == "fund_ocr_test_error":
                    self._handle_fund_ocr_test_error(data)
                elif message_type == "fund_ocr_test_complete":
                    self._handle_fund_ocr_test_complete()
                    
        except queue.Empty:
            pass
        finally:
            self.root.after(100, self.check_message_queue)
            
    def add_result_to_table(self, result):
        """添加结果到表格"""
        try:
            row_count = len(self.result_tree.get_children()) + 1
            values = [
                row_count,
                result['stock_code'],
                result.get('today_fund', 0),
                result.get('yesterday_fund', 0),
                result.get('day_before_yesterday_fund', 0),
                result.get('status', '')
            ]
            
            item = self.result_tree.insert('', 'end', values=values)
            
            # 根据状态设置颜色
            if result.get('status') == '符合条件':
                self.result_tree.item(item, tags=('success',))
            elif result.get('status') == '不符合条件':
                self.result_tree.item(item, tags=('fail',))
            else:
                self.result_tree.item(item, tags=('error',))
                
            # 配置标签颜色
            self.result_tree.tag_configure('success', background='lightgreen')
            self.result_tree.tag_configure('fail', background='lightcoral')
            self.result_tree.tag_configure('error', background='lightyellow')
            
            # 自动滚动到最新项
            self.result_tree.see(item)
            
        except Exception as e:
            self.logger.error(f"添加结果到表格失败: {str(e)}")
    
    def _handle_ocr_test_result(self, result: Dict[str, Any]):
        """处理OCR测试结果"""
        try:
            if result and not result.get('error'):
                # 格式化结果显示
                result_text = "OCR识别结果:\n\n"
                
                # 按类型分组显示
                fund_values = {}
                pct_values = {}
                
                for key, value in result.items():
                    if 'fund_pct' in key:
                        pct_values[key] = value
                    elif 'fund' in key:
                        fund_values[key] = value
                
                if fund_values:
                    result_text += "资金值 (万元):\n"
                    for key, value in fund_values.items():
                        display_name = key.replace('_', ' ').title()
                        result_text += f"  {display_name}: {value}\n"
                    result_text += "\n"
                
                if pct_values:
                    result_text += "百分比值:\n"
                    for key, value in pct_values.items():
                        display_name = key.replace('_', ' ').title()
                        result_text += f"  {display_name}: {value}%\n"
                
                if not fund_values and not pct_values:
                    result_text += "未识别到标准格式的多空资金数据\n"
                    result_text += f"原始结果: {result}"
                
                messagebox.showinfo("OCR测试结果", result_text)
                self.logger.info(f"OCR测试成功: {result}")
                
            elif result.get('error'):
                error_msg = result['error']
                messagebox.showerror("OCR测试失败", f"OCR识别出现错误:\n{error_msg}")
                self.logger.error(f"OCR测试错误: {error_msg}")
            else:
                messagebox.showwarning("OCR测试结果", "未识别到任何多空资金数据\n\n可能原因:\n1. 选择区域没有包含资金数据\n2. 图像质量不够清晰\n3. 文字格式不符合预期\n\n建议:\n1. 重新选择包含资金数据的区域\n2. 运行OCR诊断检查系统状态")
                self.logger.warning("OCR测试未识别到数据")
                
        except Exception as e:
            self.logger.error(f"处理OCR测试结果失败: {str(e)}")
            messagebox.showerror("错误", f"处理OCR测试结果失败: {str(e)}")
    
    def _handle_ocr_raw_test_result(self, result: Dict[str, Any]):
        """处理原始OCR测试结果"""
        try:
            if result and result.get('success'):
                # 格式化原始识别结果显示
                result_text = "OCR原始识别结果:\n\n"
                
                raw_results = result.get('raw_results', [])
                valid_count = result.get('valid_count', 0)
                total_count = result.get('total_count', 0)
                region = result.get('region', {})
                best_strategy = result.get('best_strategy', '未知')
                best_confidence = result.get('best_confidence', 0.0)
                
                result_text += f"测试区域: [{region.get('x', 0)}, {region.get('y', 0)}, {region.get('width', 0)}, {region.get('height', 0)}]\n"
                result_text += f"识别结果: 共 {total_count} 个结果，有效 {valid_count} 个\n"
                result_text += f"最佳策略: {best_strategy} (置信度: {best_confidence:.3f})\n\n"
                
                if raw_results:
                    # 按策略分组显示
                    strategy_groups = {}
                    for r in raw_results:
                        if 'error' not in r:
                            strategy = r.get('strategy', '未知策略')
                            if strategy not in strategy_groups:
                                strategy_groups[strategy] = []
                            strategy_groups[strategy].append(r)
                    
                    # 显示最佳结果
                    valid_results = [r for r in raw_results if 'error' not in r]
                    if valid_results:
                        best_result = max(valid_results, key=lambda x: x['confidence'])
                        result_text += "=== 📍 最佳识别结果 ===\n"
                        result_text += f"文字: \"{best_result['text']}\"\n"
                        result_text += f"置信度: {best_result['confidence']:.3f}\n"
                        result_text += f"引擎: {best_result['engine']}\n"
                        result_text += f"策略: {best_result['strategy']}\n"
                        result_text += f"位置: {best_result['position']}\n\n"
                    
                    # 按策略显示所有结果
                    result_text += "=== 📋 按策略分组的详细结果 ===\n"
                    for strategy, results in strategy_groups.items():
                        if results:  # 只显示有结果的策略
                            result_text += f"\n--- {strategy} ---\n"
                            for i, r in enumerate(results):
                                result_text += f"{i+1}. [{r['engine']}] \"{r['text']}\" (置信度: {r['confidence']:.3f})\n"
                    
                    # 显示所有错误
                    error_results = [r for r in raw_results if 'error' in r]
                    if error_results:
                        result_text += "\n=== ❌ 识别错误 ===\n"
                        error_strategies = set()
                        for r in error_results:
                            strategy_engine = f"{r.get('strategy', '未知')} + {r['engine']}"
                            if strategy_engine not in error_strategies:
                                result_text += f"• {strategy_engine}: {r['error']}\n"
                                error_strategies.add(strategy_engine)
                
                else:
                    result_text += "❌ 未识别到任何文字内容\n\n"
                    result_text += "可能原因:\n"
                    result_text += "• 选择区域没有包含清晰的文字\n"
                    result_text += "• 图像对比度不够或过度曝光\n"
                    result_text += "• 文字太小、模糊或字体特殊\n"
                    result_text += "• OCR引擎未正确初始化\n\n"
                    result_text += "建议:\n"
                    result_text += "• 重新选择包含清晰文字的区域\n"
                    result_text += "• 确保文字与背景有足够对比度\n"
                    result_text += "• 运行OCR诊断检查系统状态\n"
                
                # 创建窗口显示详细结果
                self._show_ocr_result_window(result_text)
                self.logger.info(f"多策略原始OCR测试成功，识别到 {valid_count} 个有效结果，最佳置信度: {best_confidence:.3f}")
                
            elif result.get('error'):
                error_msg = result['error']
                messagebox.showerror("OCR测试失败", f"OCR识别出现错误:\n{error_msg}")
                self.logger.error(f"原始OCR测试错误: {error_msg}")
            else:
                messagebox.showwarning("OCR测试结果", "OCR测试失败，未获得有效结果")
                self.logger.warning("原始OCR测试未获得有效结果")
                
        except Exception as e:
            self.logger.error(f"处理原始OCR测试结果失败: {str(e)}")
            messagebox.showerror("错误", f"处理原始OCR测试结果失败: {str(e)}")
    
    def _show_ocr_result_window(self, result_text: str):
        """显示OCR结果窗口"""
        try:
            # 创建新窗口
            result_window = tk.Toplevel(self.root)
            result_window.title("OCR识别结果")
            result_window.geometry("700x500")
            
            # 文本框显示结果
            text_frame = ttk.Frame(result_window)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            import tkinter.scrolledtext as scrolledtext
            result_display = scrolledtext.ScrolledText(text_frame, wrap=tk.WORD, font=("Microsoft YaHei", 10))
            result_display.pack(fill=tk.BOTH, expand=True)
            result_display.insert(tk.END, result_text)
            result_display.config(state=tk.DISABLED)
            
            # 按钮框架
            btn_frame = ttk.Frame(result_window)
            btn_frame.pack(fill=tk.X, padx=10, pady=5)
            
            # 关闭按钮
            close_btn = ttk.Button(btn_frame, text="关闭", command=result_window.destroy)
            close_btn.pack(side=tk.RIGHT)
            
        except Exception as e:
            self.logger.error(f"显示OCR结果窗口失败: {str(e)}")
            # 如果窗口创建失败，回退到简单消息框
            messagebox.showinfo("OCR识别结果", result_text[:1000] + "..." if len(result_text) > 1000 else result_text)
    
    def _handle_ocr_test_error(self, error_msg: str):
        """处理OCR测试错误"""
        self.logger.error(f"OCR测试错误: {error_msg}")
        messagebox.showerror("OCR测试失败", f"OCR识别失败:\n{error_msg}\n\n建议:\n1. 检查选择的区域是否包含文字\n2. 运行OCR诊断检查系统状态\n3. 尝试重新启动应用程序")
    
    def _handle_ocr_test_complete(self):
        """处理OCR测试完成"""
        self.test_ocr_btn.config(state=tk.NORMAL)
        self._update_ocr_status()
    
    def _handle_ocr_diagnosis_result(self, report: str):
        """处理OCR诊断结果"""
        try:
            # 创建一个新窗口显示诊断报告
            diag_window = tk.Toplevel(self.root)
            diag_window.title("OCR诊断报告")
            diag_window.geometry("800x600")
            
            # 创建文本框显示报告
            text_frame = ttk.Frame(diag_window)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            report_text = scrolledtext.ScrolledText(text_frame, wrap=tk.WORD, font=("Consolas", 10))
            report_text.pack(fill=tk.BOTH, expand=True)
            report_text.insert(tk.END, report)
            report_text.config(state=tk.DISABLED)
            
            # 按钮框架
            btn_frame = ttk.Frame(diag_window)
            btn_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
            
            # 保存报告按钮
            def save_report():
                filename = filedialog.asksaveasfilename(
                    defaultextension=".txt",
                    filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
                    title="保存诊断报告"
                )
                if filename:
                    try:
                        with open(filename, 'w', encoding='utf-8') as f:
                            f.write(report)
                        messagebox.showinfo("成功", f"诊断报告已保存到: {filename}")
                    except Exception as e:
                        messagebox.showerror("错误", f"保存失败: {str(e)}")
            
            ttk.Button(btn_frame, text="保存报告", command=save_report).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(btn_frame, text="关闭", command=diag_window.destroy).pack(side=tk.RIGHT)
            
            self.logger.info("OCR诊断完成")
            
        except Exception as e:
            self.logger.error(f"显示OCR诊断结果失败: {str(e)}")
            messagebox.showerror("错误", f"显示诊断结果失败: {str(e)}")
    
    def _handle_ocr_diagnosis_error(self, error_msg: str):
        """处理OCR诊断错误"""
        self.logger.error(f"OCR诊断错误: {error_msg}")
        messagebox.showerror("OCR诊断失败", f"OCR诊断过程中发生错误:\n{error_msg}")
    
    def _handle_fund_ocr_test_result(self, result: Dict[str, Any]):
        """处理多空资金OCR测试结果"""
        try:
            if result and result.get('success'):
                fund_values = result.get('fund_values', [])
                region = result.get('region', {})
                best_strategy = result.get('best_strategy', '未知')
                
                # 在日志中显示测试信息
                self.logger.info(f"多空资金OCR测试完成 - 区域: [{region.get('x', 0)}, {region.get('y', 0)}, {region.get('width', 0)}, {region.get('height', 0)}]")
                self.logger.info(f"最佳识别策略: {best_strategy}")
                
                if fund_values:
                    # 格式化数值：正数不显示+号，负数显示-号，不显示%号
                    formatted_values = []
                    for value in fund_values:
                        if value >= 0:
                            formatted_values.append(f"{value:.3f}")
                        else:
                            formatted_values.append(f"{value:.3f}")
                    
                    # 在日志中显示最终识别结果
                    self.logger.info(f"✓ 识别到多空资金数据: {', '.join(formatted_values)}")
                    self.logger.info(f"共识别到 {len(fund_values)} 个有效数值")
                    
                    # 显示简单的成功消息
                    messagebox.showinfo("测试成功", f"识别到 {len(fund_values)} 个多空资金数值\n结果已记录在日志中")
                else:
                    self.logger.warning("❌ 未识别到符合格式的多空资金数据")
                    messagebox.showwarning("测试结果", "未识别到多空资金数据\n\n可能原因:\n• 选择区域没有包含资金数据\n• 数据格式不符合预期(x.xxx%或-x.xxx%)\n• 图像质量不够清晰\n\n建议:\n• 重新选择包含资金数据的区域\n• 确保数据格式正确")
                
            elif result.get('error'):
                error_msg = result['error']
                self.logger.error(f"多空资金OCR测试错误: {error_msg}")
                messagebox.showerror("测试失败", f"OCR识别出现错误:\n{error_msg}")
            else:
                self.logger.warning("多空资金OCR测试未获得有效结果")
                messagebox.showwarning("测试结果", "未识别到多空资金数据\n请检查选择的区域和数据格式")
                
        except Exception as e:
            self.logger.error(f"处理多空资金OCR测试结果失败: {str(e)}")
            messagebox.showerror("错误", f"处理测试结果失败: {str(e)}")
    
    def _handle_fund_ocr_test_error(self, error_msg: str):
        """处理多空资金OCR测试错误"""
        self.logger.error(f"多空资金OCR测试错误: {error_msg}")
        messagebox.showerror("多空资金OCR测试失败", f"多空资金OCR识别失败:\n{error_msg}\n\n建议:\n1. 检查选择的区域是否包含多空资金数据\n2. 确保数据格式为x.xxx%或-x.xxx%\n3. 运行OCR诊断检查系统状态\n4. 尝试重新启动应用程序")
    
    def _handle_fund_ocr_test_complete(self):
        """处理多空资金OCR测试完成"""
        self.test_fund_ocr_btn.config(state=tk.NORMAL)
        self._update_ocr_status()
            
            
    def run(self):
        """运行GUI"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.logger.info("用户中断程序")
        except Exception as e:
            self.logger.error(f"GUI运行错误: {str(e)}")
            messagebox.showerror("错误", f"程序运行错误: {str(e)}")
        finally:
            if self.compass_automator:
                self.compass_automator.close_compass_software()


class GUILogHandler(logging.Handler):
    """GUI日志处理器"""
    
    def __init__(self, text_widget):
        super().__init__()
        self.text_widget = text_widget
        
    def emit(self, record):
        """发出日志记录"""
        try:
            msg = self.format(record)
            self.text_widget.insert(tk.END, msg + '\n')
            self.text_widget.see(tk.END)
            self.text_widget.update()
        except Exception:
            pass


def main():
    """主函数"""
    try:
        app = StockScreenerGUI()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {str(e)}")
        print(traceback.format_exc())


if __name__ == "__main__":
    main()