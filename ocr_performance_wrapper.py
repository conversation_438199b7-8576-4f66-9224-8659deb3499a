# -*- coding: utf-8 -*-
"""
OCR性能优化包装器
为现有的FundDataOCR提供性能优化功能
"""

import time
import logging
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from unified_ocr_component import FundDataOCR
from ocr_strategy_optimizer import OptimizedOCRStrategy


class OptimizedFundDataOCR:
    """优化版的资金数据OCR识别器"""
    
    def __init__(self, fund_ocr_instance: FundDataOCR):
        """
        初始化优化包装器
        
        Args:
            fund_ocr_instance: 原始的FundDataOCR实例
        """
        self.logger = logging.getLogger(__name__)
        self.fund_ocr = fund_ocr_instance
        self.strategy_optimizer = OptimizedOCRStrategy(fund_ocr_instance)
        
        # 优化配置
        self.enable_early_exit = True
        self.enable_strategy_reordering = True
        self.strategy_success_history = {}
        
        # 性能统计
        self.optimization_stats = {
            'total_calls': 0,
            'early_exits': 0,
            'time_saved': 0.0,
            'original_avg_time': 0.0,
            'optimized_avg_time': 0.0
        }
    
    def test_fund_data_ocr_recognition_optimized(self, x: int, y: int, width: int, height: int) -> Dict[str, Any]:
        """
        优化版的多空资金OCR识别
        
        Args:
            x, y: 识别区域左上角坐标
            width, height: 识别区域尺寸
            
        Returns:
            识别结果字典
        """
        start_time = time.time()
        self.optimization_stats['total_calls'] += 1
        
        try:
            # 获取截图
            screenshot = self.fund_ocr._take_screenshot(x, y, width, height)
            if screenshot is None:
                return {'success': False, 'error': '截图失败'}
            
            # 定义优化后的策略顺序（基于历史成功率排序）
            strategies = self._get_optimized_strategies(screenshot)
            
            # 使用优化的策略执行器
            def recognition_func(processed_image: np.ndarray, strategy_name: str) -> Dict[str, Any]:
                return self._execute_single_strategy(processed_image, strategy_name, x, y, width, height)
            
            result = self.strategy_optimizer.execute_strategies_with_early_exit(
                screenshot, strategies, recognition_func
            )
            
            # 处理结果
            if result.get('success', False):
                fund_values = self._extract_fund_values_from_result(result)
                
                # 构建最终返回结果
                final_result = {
                    'success': True,
                    'fund_values': fund_values,
                    'region': {'x': x, 'y': y, 'width': width, 'height': height},
                    'strategies_tried': result.get('strategies_tried', 0),
                    'early_exit': result.get('early_exit', False),
                    'execution_time': result.get('execution_time', 0),
                    'best_strategy': self._get_best_strategy_name(result),
                    'best_confidence': self._get_best_confidence(result),
                    'optimization_applied': True
                }
                
                # 更新统计信息
                self._update_optimization_stats(start_time, result)
                
                return final_result
            else:
                # 如果优化版本失败，回退到原始版本
                self.logger.debug("优化版本失败，回退到原始版本")
                return self.fund_ocr.test_fund_data_ocr_recognition(x, y, width, height)
                
        except Exception as e:
            self.logger.error(f"优化OCR识别失败: {str(e)}")
            # 发生异常时回退到原始版本
            return self.fund_ocr.test_fund_data_ocr_recognition(x, y, width, height)
    
    def _get_optimized_strategies(self, screenshot: np.ndarray) -> List[Tuple[str, Any]]:
        """
        获取优化后的策略列表（基于历史成功率排序）
        
        Args:
            screenshot: 截图
            
        Returns:
            策略列表
        """
        # 基础策略列表
        base_strategies = [
            ("无预处理", screenshot),
            ("百分比专用v2(保守)", lambda img: self.fund_ocr._preprocess_for_percentage_v2(img)),
            ("百分比专用v1(标准)", lambda img: self.fund_ocr._preprocess_for_percentage(img)),
            ("放大3倍", lambda img: self.fund_ocr._preprocess_scale_only(img, 3.0)),
            ("放大2倍", lambda img: self.fund_ocr._preprocess_scale_only(img, 2.0)),
            ("简化预处理", lambda img: self.fund_ocr._preprocess_image_simple(img)),
            ("仅灰度化", lambda img: self.fund_ocr._convert_to_grayscale(img)),
            ("CPU优化", lambda img: self.fund_ocr._preprocess_image_cpu_optimized(img)),
        ]
        
        # 如果启用策略重排序，根据历史成功率排序
        if self.enable_strategy_reordering and self.strategy_success_history:
            base_strategies.sort(key=lambda x: self.strategy_success_history.get(x[0], 0), reverse=True)
            self.logger.debug("策略已根据历史成功率重新排序")
        
        return base_strategies
    
    def _execute_single_strategy(self, processed_image: np.ndarray, strategy_name: str, 
                                x: int, y: int, width: int, height: int) -> Dict[str, Any]:
        """
        执行单个策略的OCR识别
        
        Args:
            processed_image: 预处理后的图像
            strategy_name: 策略名称
            x, y, width, height: 区域信息
            
        Returns:
            识别结果
        """
        try:
            # 保存调试图像
            if self.fund_ocr.save_debug_images:
                self.fund_ocr._save_debug_image(processed_image, f"optimized_{strategy_name}_{x}_{y}_{width}_{height}")
            
            # 执行OCR识别
            strategy_results = self.fund_ocr._multi_engine_ocr_with_relaxed_params(processed_image, strategy_name)
            
            if strategy_results:
                # 更新策略成功历史
                self._update_strategy_success(strategy_name, True)
                
                return {
                    'success': True,
                    'strategy_name': strategy_name,
                    'results': strategy_results,
                    'best_confidence': self._calculate_confidence(strategy_results)
                }
            else:
                # 更新策略失败历史
                self._update_strategy_success(strategy_name, False)
                
                return {
                    'success': False,
                    'strategy_name': strategy_name,
                    'error': '未识别到有效数据'
                }
                
        except Exception as e:
            self.logger.error(f"执行策略 {strategy_name} 失败: {str(e)}")
            self._update_strategy_success(strategy_name, False)
            return {
                'success': False,
                'strategy_name': strategy_name,
                'error': str(e)
            }
    
    def _extract_fund_values_from_result(self, result: Dict[str, Any]) -> List[float]:
        """从结果中提取资金数值"""
        fund_values = []
        
        try:
            # 从累积数据中提取
            accumulated_data = result.get('accumulated_data', {})
            
            # 按优先级提取数值
            for i in range(10):  # 最多提取10个数值
                key = f'fund_{i}'
                if key in accumulated_data:
                    fund_values.append(accumulated_data[key])
            
            # 如果没有找到fund_*格式的数据，尝试其他格式
            if not fund_values:
                for key in ['today_fund', 'yesterday_fund', 'day_before_yesterday_fund']:
                    if key in accumulated_data:
                        fund_values.append(accumulated_data[key])
            
            # 如果仍然没有数据，从最佳结果中提取
            if not fund_values and result.get('best_result'):
                best_result = result['best_result']
                if 'fund_values' in best_result:
                    fund_values = best_result['fund_values']
                elif 'results' in best_result:
                    # 尝试从results中提取
                    results = best_result['results']
                    if isinstance(results, dict):
                        for key, value in results.items():
                            if isinstance(value, (int, float)):
                                fund_values.append(value)
            
            return fund_values[:3]  # 最多返回3个数值
            
        except Exception as e:
            self.logger.error(f"提取资金数值失败: {str(e)}")
            return []
    
    def _get_best_strategy_name(self, result: Dict[str, Any]) -> str:
        """获取最佳策略名称"""
        try:
            best_result = result.get('best_result')
            if best_result and 'strategy_name' in best_result:
                return best_result['strategy_name']
            
            # 如果没有最佳结果，返回第一个成功的策略
            all_results = result.get('all_strategy_results', [])
            for strategy_result in all_results:
                if strategy_result.get('result', {}).get('success', False):
                    return strategy_result.get('strategy', '未知')
            
            return '未知'
        except Exception:
            return '未知'
    
    def _get_best_confidence(self, result: Dict[str, Any]) -> float:
        """获取最佳置信度"""
        try:
            best_result = result.get('best_result')
            if best_result and 'best_confidence' in best_result:
                return best_result['best_confidence']
            return 0.0
        except Exception:
            return 0.0
    
    def _calculate_confidence(self, strategy_results: Dict[str, Any]) -> float:
        """计算策略结果的置信度"""
        try:
            # 简单的置信度计算：基于识别到的数据数量
            valid_count = sum(1 for value in strategy_results.values() if isinstance(value, (int, float)))
            return min(1.0, valid_count / 3.0)  # 最多3个数据，每个贡献1/3置信度
        except Exception:
            return 0.0
    
    def _update_strategy_success(self, strategy_name: str, success: bool):
        """更新策略成功历史"""
        if strategy_name not in self.strategy_success_history:
            self.strategy_success_history[strategy_name] = 0.5  # 初始成功率50%
        
        # 使用加权平均更新成功率
        current_rate = self.strategy_success_history[strategy_name]
        new_rate = current_rate * 0.9 + (1.0 if success else 0.0) * 0.1
        self.strategy_success_history[strategy_name] = new_rate
    
    def _update_optimization_stats(self, start_time: float, result: Dict[str, Any]):
        """更新优化统计信息"""
        execution_time = time.time() - start_time
        
        # 更新平均时间
        total_calls = self.optimization_stats['total_calls']
        old_avg = self.optimization_stats['optimized_avg_time']
        self.optimization_stats['optimized_avg_time'] = (old_avg * (total_calls - 1) + execution_time) / total_calls
        
        # 统计早期退出
        if result.get('early_exit', False):
            self.optimization_stats['early_exits'] += 1
        
        # 估算节省的时间（基于策略数量差异）
        strategies_tried = result.get('strategies_tried', 0)
        total_strategies = 8  # 总策略数
        if strategies_tried < total_strategies:
            saved_strategies = total_strategies - strategies_tried
            estimated_time_per_strategy = execution_time / max(1, strategies_tried)
            estimated_saved_time = saved_strategies * estimated_time_per_strategy
            self.optimization_stats['time_saved'] += estimated_saved_time
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """获取优化统计信息"""
        stats = self.optimization_stats.copy()
        
        # 添加成功率信息
        if stats['total_calls'] > 0:
            stats['early_exit_rate'] = stats['early_exits'] / stats['total_calls']
        else:
            stats['early_exit_rate'] = 0.0
        
        # 添加策略优化器的统计信息
        strategy_stats = self.strategy_optimizer.get_performance_stats()
        stats['strategy_optimizer'] = strategy_stats
        
        # 添加策略成功历史
        stats['strategy_success_history'] = self.strategy_success_history
        
        return stats
    
    def reset_optimization_stats(self):
        """重置优化统计信息"""
        self.optimization_stats = {
            'total_calls': 0,
            'early_exits': 0,
            'time_saved': 0.0,
            'original_avg_time': 0.0,
            'optimized_avg_time': 0.0
        }
        self.strategy_success_history.clear()
        self.strategy_optimizer.clear_cache()
        self.logger.info("OCR优化统计信息已重置")


def create_optimized_ocr_wrapper(fund_ocr_instance: FundDataOCR) -> OptimizedFundDataOCR:
    """
    创建优化版OCR包装器
    
    Args:
        fund_ocr_instance: 原始FundDataOCR实例
        
    Returns:
        优化版OCR包装器
    """
    return OptimizedFundDataOCR(fund_ocr_instance)