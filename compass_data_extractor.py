# -*- coding: utf-8 -*-
"""
指南针数据提取模块
负责从指南针软件中提取股票资金流数据
"""

import time
import logging
from typing import Dict, Any, Optional

try:
    import pywinauto
except ImportError:
    pywinauto = None

from config import COMPASS_SOFTWARE, APP_CONFIG
from data_processor import parse_fund_value
from ocr_manager_optimized import get_global_ocr_manager


class CompassDataExtractor:
    """指南针数据提取器"""
    
    def __init__(self, main_window=None):
        """
        初始化数据提取器
        
        Args:
            main_window: 主窗口对象
        """
        self.logger = logging.getLogger(__name__)
        self.main_window = main_window
        
        # 使用全局OCR管理器，不再在这里创建新的OCR实例
        self.ocr_manager = get_global_ocr_manager()
    
    def get_fund_flow_data(self, stock_code: str) -> Dict[str, float]:
        """
        获取股票的多空资金数据（优先使用OCR模式）
        
        Args:
            stock_code: 股票代码
            
        Returns:
            包含三天资金数据的字典
        """
        try:
            # 优先使用OCR模式
            if APP_CONFIG.get('use_ocr_mode', True):
                self.logger.info(f"使用OCR模式获取股票 {stock_code} 的资金数据")
                return self._get_fund_flow_by_ocr(stock_code)
            else:
                self.logger.info(f"使用控件模式获取股票 {stock_code} 的资金数据")
                return self._get_fund_flow_by_controls(stock_code)
                
        except Exception as e:
            self.logger.error(f"获取股票 {stock_code} 资金数据失败: {str(e)}")
            return {}
    
    def _get_fund_flow_by_controls(self, stock_code: str) -> Dict[str, float]:
        """
        通过控件读取资金流数据
        
        Args:
            stock_code: 股票代码
            
        Returns:
            资金数据字典
        """
        fund_data = {}
        
        try:
            # 获取今天的资金数据
            today_control = self._find_control(
                COMPASS_SOFTWARE['fund_flow_area']['today'],
                "今日资金"
            )
            if today_control:
                today_text = today_control.window_text()
                fund_data['today_fund'] = parse_fund_value(today_text)
                self.logger.debug(f"今日资金: {today_text} -> {fund_data['today_fund']}")
            
            # 获取昨天的资金数据
            yesterday_control = self._find_control(
                COMPASS_SOFTWARE['fund_flow_area']['yesterday'],
                "昨日资金"
            )
            if yesterday_control:
                yesterday_text = yesterday_control.window_text()
                fund_data['yesterday_fund'] = parse_fund_value(yesterday_text)
                self.logger.debug(f"昨日资金: {yesterday_text} -> {fund_data['yesterday_fund']}")
            
            # 获取前天的资金数据
            day_before_control = self._find_control(
                COMPASS_SOFTWARE['fund_flow_area']['day_before_yesterday'],
                "前日资金"
            )
            if day_before_control:
                day_before_text = day_before_control.window_text()
                fund_data['day_before_yesterday_fund'] = parse_fund_value(day_before_text)
                self.logger.debug(f"前日资金: {day_before_text} -> {fund_data['day_before_yesterday_fund']}")
            
            return fund_data
            
        except Exception as e:
            self.logger.error(f"通过控件读取资金数据失败: {str(e)}")
            return {}
    
    def _get_fund_flow_by_ocr(self, stock_code: str) -> Dict[str, float]:
        """
        通过OCR读取资金流数据
        
        Args:
            stock_code: 股票代码
            
        Returns:
            资金数据字典
        """
        try:
            # 检查OCR管理器是否已初始化
            if not self.ocr_manager.is_initialized():
                self.logger.error("OCR管理器未初始化")
                return {}
            
            # 获取优化的OCR引擎实例
            ocr_engine = self.ocr_manager.get_optimized_ocr_engine()
            if not ocr_engine:
                self.logger.error("无法获取OCR引擎实例")
                return {}
                
            if not self.main_window:
                self.logger.error("主窗口对象为空，无法进行OCR识别")
                return {}
            
            # 获取主窗口位置和OCR区域配置
            rect = self.main_window.rectangle()
            ocr_region = COMPASS_SOFTWARE['ocr_region']
            
            # 计算绝对坐标
            x = rect.left + ocr_region['x']
            y = rect.top + ocr_region['y']
            width = ocr_region['width']
            height = ocr_region['height']
            
            self.logger.info(f"开始OCR识别股票 {stock_code} 的资金数据，区域: ({x}, {y}, {width}, {height})")
            
            # 使用优化的OCR引擎进行识别
            if hasattr(ocr_engine, 'test_fund_data_ocr_recognition_optimized'):
                # 使用优化版本
                ocr_result = ocr_engine.test_fund_data_ocr_recognition_optimized(x, y, width, height)
                self.logger.debug(f"使用优化版OCR引擎，早期退出: {ocr_result.get('early_exit', False)}")
            else:
                # 回退到原始版本
                ocr_result = ocr_engine.test_fund_data_ocr_recognition(x, y, width, height)
                self.logger.debug("使用原始OCR引擎")
            
            if not ocr_result.get('success'):
                error_msg = ocr_result.get('error', '未知错误')
                self.logger.error(f"OCR识别失败: {error_msg}")
                return {}
            
            # 从识别结果中提取资金数据
            fund_values = ocr_result.get('fund_values', [])
            fund_data = {}
            
            if len(fund_values) >= 3:
                # 按顺序分配：今天、昨天、前天
                fund_data['today_fund'] = fund_values[0]
                fund_data['yesterday_fund'] = fund_values[1] 
                fund_data['day_before_yesterday_fund'] = fund_values[2]
            elif len(fund_values) == 2:
                fund_data['today_fund'] = fund_values[0]
                fund_data['yesterday_fund'] = fund_values[1]
                fund_data['day_before_yesterday_fund'] = 0.0
            elif len(fund_values) == 1:
                fund_data['today_fund'] = fund_values[0]
                fund_data['yesterday_fund'] = 0.0
                fund_data['day_before_yesterday_fund'] = 0.0
            else:
                self.logger.warning(f"OCR未识别到有效的资金数据")
                return {}
            
            self.logger.info(f"OCR识别到的资金数据: {fund_data}")
            self.logger.info(f"识别策略: {ocr_result.get('best_strategy', '未知')} (置信度: {ocr_result.get('best_confidence', 0):.3f})")
            
            # 记录优化信息
            if ocr_result.get('optimization_applied', False):
                strategies_tried = ocr_result.get('strategies_tried', 0)
                early_exit = ocr_result.get('early_exit', False)
                execution_time = ocr_result.get('execution_time', 0)
                self.logger.debug(f"OCR优化效果：尝试{strategies_tried}个策略，早期退出:{early_exit}，耗时{execution_time:.2f}s")
            
            return fund_data
            
        except Exception as e:
            self.logger.error(f"OCR读取资金数据失败: {str(e)}")
            return {}
    
    def _find_control(self, control_config: Dict[str, Any], control_name: str):
        """
        查找UI控件
        
        Args:
            control_config: 控件配置
            control_name: 控件名称（用于日志）
            
        Returns:
            找到的控件或None
        """
        try:
            if not self.main_window:
                self.logger.error("主窗口对象为空，无法查找控件")
                return None
                
            # 尝试多种方式查找控件
            search_criteria = {}
            
            if control_config.get('auto_id'):
                search_criteria['auto_id'] = control_config['auto_id']
            
            if control_config.get('control_type'):
                search_criteria['control_type'] = control_config['control_type']
            
            if control_config.get('class_name'):
                search_criteria['class_name'] = control_config['class_name']
            
            # 查找控件
            control = self.main_window.child_window(**search_criteria)
            
            if control.exists():
                return control
            
            self.logger.warning(f"未找到控件: {control_name}")
            return None
            
        except Exception as e:
            self.logger.error(f"查找控件 {control_name} 失败: {str(e)}")
            return None
    
    def set_main_window(self, main_window):
        """
        设置主窗口对象
        
        Args:
            main_window: 主窗口对象
        """
        self.main_window = main_window