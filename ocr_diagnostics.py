#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR诊断工具
用于检测、诊断和修复OCR相关问题
"""

import os
import sys
import logging
import traceback
from typing import Dict, List, Optional, Tuple, Any
import cv2
import numpy as np
import easyocr
import paddleocr
from PIL import Image, ImageDraw, ImageFont
import matplotlib.pyplot as plt
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OCRDiagnostics:
    """OCR诊断工具类"""
    
    def __init__(self):
        self.easyocr_reader = None
        self.paddleocr_reader = None
        self.diagnostic_results = {}
        self.test_images = []
        
    def check_system_requirements(self) -> Dict[str, Any]:
        """检查系统要求"""
        results = {
            'python_version': sys.version,
            'opencv_available': False,
            'easyocr_available': False,
            'paddleocr_available': False,
            'pillow_available': False,
            'numpy_available': False,
            'matplotlib_available': False,
            'gpu_available': False,
            'recommendations': []
        }
        
        # 检查OpenCV
        try:
            import cv2
            results['opencv_available'] = True
            results['opencv_version'] = cv2.__version__
        except ImportError:
            results['recommendations'].append("安装OpenCV: pip install opencv-python")
            
        # 检查EasyOCR
        try:
            import easyocr
            results['easyocr_available'] = True
            results['easyocr_version'] = easyocr.__version__
        except ImportError:
            results['recommendations'].append("安装EasyOCR: pip install easyocr")
            
        # 检查PaddleOCR
        try:
            import paddleocr
            results['paddleocr_available'] = True
        except ImportError:
            results['recommendations'].append("安装PaddleOCR: pip install paddleocr")
            
        # 检查其他依赖
        try:
            import PIL
            results['pillow_available'] = True
        except ImportError:
            results['recommendations'].append("安装Pillow: pip install Pillow")
            
        try:
            import numpy
            results['numpy_available'] = True
        except ImportError:
            results['recommendations'].append("安装NumPy: pip install numpy")
            
        try:
            import matplotlib
            results['matplotlib_available'] = True
        except ImportError:
            results['recommendations'].append("安装Matplotlib: pip install matplotlib")
            
        # 检查GPU支持
        try:
            import torch
            if torch.cuda.is_available():
                results['gpu_available'] = True
                results['gpu_count'] = torch.cuda.device_count()
                results['gpu_names'] = [torch.cuda.get_device_name(i) for i in range(torch.cuda.device_count())]
        except ImportError:
            pass
            
        return results
        
    def initialize_ocr_engines(self) -> Dict[str, bool]:
        """初始化OCR引擎"""
        results = {'easyocr': False, 'paddleocr': False, 'errors': []}
        
        # 初始化EasyOCR
        try:
            self.easyocr_reader = easyocr.Reader(['ch_sim', 'en'], gpu=True)
            results['easyocr'] = True
            logger.info("EasyOCR引擎初始化成功")
        except Exception as e:
            error_msg = f"EasyOCR初始化失败: {str(e)}"
            results['errors'].append(error_msg)
            logger.error(error_msg)
            
            # 尝试CPU模式
            try:
                self.easyocr_reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
                results['easyocr'] = True
                logger.info("EasyOCR引擎初始化成功 (CPU模式)")
            except Exception as e2:
                error_msg = f"EasyOCR CPU模式初始化也失败: {str(e2)}"
                results['errors'].append(error_msg)
                logger.error(error_msg)
                
        # 初始化PaddleOCR
        try:
            self.paddleocr_reader = paddleocr.PaddleOCR(use_angle_cls=True, lang='ch')
            results['paddleocr'] = True
            logger.info("PaddleOCR引擎初始化成功")
        except Exception as e:
            error_msg = f"PaddleOCR初始化失败: {str(e)}"
            results['errors'].append(error_msg)
            logger.error(error_msg)
            
        return results
        
    def create_test_images(self) -> List[str]:
        """创建测试图片"""
        test_images = []
        
        # 测试图片1: 简单数字
        img1 = Image.new('RGB', (200, 100), color='white')
        draw1 = ImageDraw.Draw(img1)
        try:
            font = ImageFont.truetype("arial.ttf", 36)
        except:
            font = ImageFont.load_default()
        draw1.text((10, 30), "123.45万", fill='black', font=font)
        img1_path = "test_image_1.png"
        img1.save(img1_path)
        test_images.append(img1_path)
        
        # 测试图片2: 复杂文本
        img2 = Image.new('RGB', (300, 150), color='white')
        draw2 = ImageDraw.Draw(img2)
        draw2.text((10, 30), "主力净流入", fill='black', font=font)
        draw2.text((10, 70), "567.89万", fill='red', font=font)
        img2_path = "test_image_2.png"
        img2.save(img2_path)
        test_images.append(img2_path)
        
        # 测试图片3: 低质量图片
        img3 = Image.new('RGB', (150, 80), color='gray')
        draw3 = ImageDraw.Draw(img3)
        draw3.text((5, 20), "1.23万", fill='darkblue', font=font)
        img3_path = "test_image_3.png"
        img3.save(img3_path)
        test_images.append(img3_path)
        
        self.test_images = test_images
        return test_images
        
    def test_ocr_accuracy(self, image_path: str) -> Dict[str, Any]:
        """测试OCR准确性"""
        results = {
            'image_path': image_path,
            'easyocr_result': None,
            'paddleocr_result': None,
            'easyocr_success': False,
            'paddleocr_success': False,
            'easyocr_time': 0,
            'paddleocr_time': 0,
            'errors': []
        }
        
        if not os.path.exists(image_path):
            results['errors'].append(f"图片文件不存在: {image_path}")
            return results
            
        # 测试EasyOCR
        if self.easyocr_reader:
            try:
                start_time = datetime.now()
                easyocr_results = self.easyocr_reader.readtext(image_path)
                end_time = datetime.now()
                results['easyocr_time'] = (end_time - start_time).total_seconds()
                results['easyocr_result'] = easyocr_results
                results['easyocr_success'] = True
                logger.info(f"EasyOCR识别成功: {easyocr_results}")
            except Exception as e:
                error_msg = f"EasyOCR识别失败: {str(e)}"
                results['errors'].append(error_msg)
                logger.error(error_msg)
                
        # 测试PaddleOCR
        if self.paddleocr_reader:
            try:
                start_time = datetime.now()
                paddleocr_results = self.paddleocr_reader.ocr(image_path, cls=True)
                end_time = datetime.now()
                results['paddleocr_time'] = (end_time - start_time).total_seconds()
                results['paddleocr_result'] = paddleocr_results
                results['paddleocr_success'] = True
                logger.info(f"PaddleOCR识别成功: {paddleocr_results}")
            except Exception as e:
                error_msg = f"PaddleOCR识别失败: {str(e)}"
                results['errors'].append(error_msg)
                logger.error(error_msg)
                
        return results
        
    def analyze_image_quality(self, image_path: str) -> Dict[str, Any]:
        """分析图片质量"""
        results = {
            'image_path': image_path,
            'resolution': None,
            'color_mode': None,
            'brightness': None,
            'contrast': None,
            'sharpness': None,
            'recommendations': []
        }
        
        try:
            # 使用PIL分析
            with Image.open(image_path) as img:
                results['resolution'] = img.size
                results['color_mode'] = img.mode
                
            # 使用OpenCV分析
            img_cv = cv2.imread(image_path)
            if img_cv is not None:
                # 计算亮度
                gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
                results['brightness'] = np.mean(gray)
                
                # 计算对比度
                results['contrast'] = np.std(gray)
                
                # 计算清晰度 (Laplacian方差)
                laplacian = cv2.Laplacian(gray, cv2.CV_64F)
                results['sharpness'] = laplacian.var()
                
                # 给出建议
                if results['brightness'] < 50:
                    results['recommendations'].append("图片太暗，建议增加亮度")
                elif results['brightness'] > 200:
                    results['recommendations'].append("图片太亮，建议降低亮度")
                    
                if results['contrast'] < 30:
                    results['recommendations'].append("对比度太低，建议增强对比度")
                    
                if results['sharpness'] < 100:
                    results['recommendations'].append("图片模糊，建议使用更清晰的图片")
                    
                if results['resolution'][0] < 100 or results['resolution'][1] < 50:
                    results['recommendations'].append("分辨率太低，建议使用更高分辨率图片")
                    
        except Exception as e:
            results['error'] = str(e)
            logger.error(f"图片质量分析失败: {str(e)}")
            
        return results
        
    def generate_diagnostic_report(self) -> str:
        """生成诊断报告"""
        report = []
        report.append("=== OCR诊断报告 ===")
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # 系统要求检查
        sys_req = self.check_system_requirements()
        report.append("1. 系统要求检查:")
        report.append(f"   Python版本: {sys_req['python_version']}")
        report.append(f"   OpenCV: {'✓' if sys_req['opencv_available'] else '✗'}")
        report.append(f"   EasyOCR: {'✓' if sys_req['easyocr_available'] else '✗'}")
        report.append(f"   PaddleOCR: {'✓' if sys_req['paddleocr_available'] else '✗'}")
        report.append(f"   GPU支持: {'✓' if sys_req['gpu_available'] else '✗'}")
        
        if sys_req['recommendations']:
            report.append("   建议:")
            for rec in sys_req['recommendations']:
                report.append(f"   - {rec}")
        report.append("")
        
        # OCR引擎初始化
        init_results = self.initialize_ocr_engines()
        report.append("2. OCR引擎初始化:")
        report.append(f"   EasyOCR: {'✓' if init_results['easyocr'] else '✗'}")
        report.append(f"   PaddleOCR: {'✓' if init_results['paddleocr'] else '✗'}")
        
        if init_results['errors']:
            report.append("   错误:")
            for error in init_results['errors']:
                report.append(f"   - {error}")
        report.append("")
        
        # 测试图片OCR
        if hasattr(self, 'test_images') and self.test_images:
            report.append("3. OCR准确性测试:")
            for i, image_path in enumerate(self.test_images, 1):
                test_result = self.test_ocr_accuracy(image_path)
                report.append(f"   测试图片{i} ({image_path}):")
                report.append(f"     EasyOCR: {'✓' if test_result['easyocr_success'] else '✗'} ({test_result['easyocr_time']:.2f}s)")
                report.append(f"     PaddleOCR: {'✓' if test_result['paddleocr_success'] else '✗'} ({test_result['paddleocr_time']:.2f}s)")
                
                if test_result['errors']:
                    for error in test_result['errors']:
                        report.append(f"     错误: {error}")
                        
                # 图片质量分析
                quality = self.analyze_image_quality(image_path)
                if quality.get('recommendations'):
                    report.append("     图片质量建议:")
                    for rec in quality['recommendations']:
                        report.append(f"     - {rec}")
            report.append("")
        
        # 总结和建议
        report.append("4. 总结和建议:")
        
        working_engines = []
        if init_results.get('easyocr'):
            working_engines.append("EasyOCR")
        if init_results.get('paddleocr'):
            working_engines.append("PaddleOCR")
            
        if working_engines:
            report.append(f"   可用的OCR引擎: {', '.join(working_engines)}")
        else:
            report.append("   ⚠️ 没有可用的OCR引擎！请检查安装和配置。")
            
        if not sys_req['gpu_available']:
            report.append("   建议: 安装CUDA和PyTorch GPU版本以提高OCR性能")
            
        report.append("")
        report.append("=== 诊断报告结束 ===")
        
        return "\n".join(report)
        
    def cleanup_test_files(self):
        """清理测试文件"""
        for image_path in self.test_images:
            try:
                if os.path.exists(image_path):
                    os.remove(image_path)
                    logger.info(f"已删除测试文件: {image_path}")
            except Exception as e:
                logger.error(f"删除测试文件失败 {image_path}: {str(e)}")
                
    def run_full_diagnostics(self) -> str:
        """运行完整诊断"""
        try:
            logger.info("开始OCR诊断...")
            
            # 创建测试图片
            self.create_test_images()
            
            # 生成报告
            report = self.generate_diagnostic_report()
            
            # 保存报告
            report_file = f"ocr_diagnostic_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            logger.info(f"诊断报告已保存到: {report_file}")
            
            # 清理测试文件
            self.cleanup_test_files()
            
            return report
            
        except Exception as e:
            error_msg = f"诊断过程中发生错误: {str(e)}\n{traceback.format_exc()}"
            logger.error(error_msg)
            return error_msg

def main():
    """主函数"""
    print("OCR诊断工具启动...")
    
    diagnostics = OCRDiagnostics()
    report = diagnostics.run_full_diagnostics()
    
    print("\n" + "="*50)
    print(report)
    print("="*50)
    
    input("\n按Enter键退出...")

if __name__ == "__main__":
    main()