# -*- coding: utf-8 -*-
"""
OCR引擎管理器单例模块 - 优化版
管理全局唯一的OCR引擎实例，避免重复初始化，并提供性能优化功能
"""

import threading
import logging
from typing import Optional, Dict, Any
from unified_ocr_component import FundDataOCR
from config import APP_CONFIG


class OCRManager:
    """OCR引擎管理器单例类"""
    
    _instance = None
    _lock = threading.Lock()
    _fund_ocr = None
    _optimized_ocr = None
    _initialized = False
    
    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(OCRManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化OCR管理器"""
        if not self._initialized:
            self.logger = logging.getLogger(__name__)
            self._initialized = True
    
    def initialize_ocr(self, use_gpu: bool = None, debug_mode: bool = None, 
                      save_debug_images: bool = None) -> bool:
        """
        初始化OCR引擎
        
        Args:
            use_gpu: 是否使用GPU，默认从配置读取
            debug_mode: 是否开启调试模式，默认从配置读取
            save_debug_images: 是否保存调试图像，默认从配置读取
            
        Returns:
            初始化是否成功
        """
        if self._fund_ocr is not None:
            self.logger.info("OCR引擎已初始化，无需重复初始化")
            return True
            
        try:
            # 从配置获取默认值
            ocr_config = getattr(APP_CONFIG, 'ocr_settings', {})
            if use_gpu is None:
                use_gpu = ocr_config.get('use_gpu', True)
            if debug_mode is None:
                debug_mode = ocr_config.get('debug_mode', False)
            if save_debug_images is None:
                save_debug_images = ocr_config.get('save_debug_images', False)
            
            self.logger.info(f"正在初始化OCR引擎... (GPU: {use_gpu}, 调试: {debug_mode})")
            
            self._fund_ocr = FundDataOCR(
                use_gpu=use_gpu,
                debug_mode=debug_mode,
                save_debug_images=save_debug_images
            )
            
            # 检查引擎状态
            status = self._fund_ocr.get_engine_status()
            available_engines = [name for name, available in status['available_engines'].items() if available]
            
            if available_engines:
                self.logger.info(f"OCR引擎初始化成功，可用引擎: {', '.join(available_engines)}")
                
                # 延迟导入以避免循环依赖
                try:
                    from ocr_performance_wrapper import create_optimized_ocr_wrapper
                    self._optimized_ocr = create_optimized_ocr_wrapper(self._fund_ocr)
                    self.logger.info("OCR性能优化包装器已创建")
                except ImportError as ie:
                    self.logger.warning(f"无法导入OCR优化包装器: {str(ie)}")
                    self._optimized_ocr = None
                
                return True
            else:
                self.logger.error("OCR引擎初始化失败：无可用引擎")
                self._fund_ocr = None
                return False
                
        except Exception as e:
            self.logger.error(f"OCR引擎初始化失败: {str(e)}")
            self._fund_ocr = None
            
            # 尝试CPU模式作为后备
            try:
                self.logger.info("尝试使用CPU模式初始化OCR引擎...")
                self._fund_ocr = FundDataOCR(use_gpu=False, debug_mode=True)
                status = self._fund_ocr.get_engine_status()
                available_engines = [name for name, available in status['available_engines'].items() if available]
                
                if available_engines:
                    self.logger.info(f"OCR引擎初始化成功（CPU模式），可用引擎: {', '.join(available_engines)}")
                    
                    # 尝试创建优化版OCR包装器
                    try:
                        from ocr_performance_wrapper import create_optimized_ocr_wrapper
                        self._optimized_ocr = create_optimized_ocr_wrapper(self._fund_ocr)
                        self.logger.info("OCR性能优化包装器已创建（CPU模式）")
                    except ImportError as ie:
                        self.logger.warning(f"无法导入OCR优化包装器: {str(ie)}")
                        self._optimized_ocr = None
                    
                    return True
                else:
                    self.logger.error("CPU模式下也无可用引擎")
                    self._fund_ocr = None
                    return False
                    
            except Exception as e2:
                self.logger.error(f"OCR引擎初始化完全失败: {str(e2)}")
                self._fund_ocr = None
                return False
    
    def get_ocr_engine(self) -> Optional[FundDataOCR]:
        """
        获取原始OCR引擎实例
        
        Returns:
            OCR引擎实例或None
        """
        if self._fund_ocr is None:
            self.logger.warning("OCR引擎未初始化，尝试自动初始化...")
            if not self.initialize_ocr():
                self.logger.error("自动初始化OCR引擎失败")
                return None
        
        return self._fund_ocr
    
    def get_optimized_ocr_engine(self):
        """
        获取优化版OCR引擎实例
        
        Returns:
            优化版OCR引擎实例或原始OCR引擎实例
        """
        if self._optimized_ocr is None:
            self.logger.warning("优化版OCR引擎未初始化，使用原始OCR引擎")
            return self.get_ocr_engine()
        
        return self._optimized_ocr
    
    def is_initialized(self) -> bool:
        """
        检查OCR引擎是否已初始化
        
        Returns:
            是否已初始化
        """
        return self._fund_ocr is not None
    
    def get_engine_status(self) -> Dict[str, Any]:
        """
        获取OCR引擎状态
        
        Returns:
            引擎状态信息
        """
        if self._fund_ocr is None:
            return {
                'initialized': False,
                'available_engines': {},
                'error_count': 0
            }
        
        try:
            status = self._fund_ocr.get_engine_status()
            status['initialized'] = True
            status['optimization_enabled'] = self._optimized_ocr is not None
            return status
        except Exception as e:
            self.logger.error(f"获取OCR引擎状态失败: {str(e)}")
            return {
                'initialized': False,
                'available_engines': {},
                'error_count': 999,
                'error': str(e)
            }
    
    def reset_error_count(self):
        """重置错误计数"""
        if self._fund_ocr:
            self._fund_ocr.reset_error_count()
            self.logger.debug("OCR错误计数已重置")
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """获取优化统计信息"""
        if self._optimized_ocr and hasattr(self._optimized_ocr, 'get_optimization_stats'):
            return self._optimized_ocr.get_optimization_stats()
        return {'optimization_available': False}
    
    def cleanup(self):
        """清理OCR引擎资源"""
        try:
            # 记录优化统计信息
            if self._optimized_ocr and hasattr(self._optimized_ocr, 'get_optimization_stats'):
                stats = self._optimized_ocr.get_optimization_stats()
                if stats.get('total_calls', 0) > 0:
                    self.logger.info(f"OCR优化统计：调用{stats['total_calls']}次，"
                                   f"早期退出{stats['early_exits']}次，"
                                   f"节省时间{stats.get('time_saved', 0):.2f}s")
            
            # 清理资源
            if self._fund_ocr:
                self.logger.info("清理OCR引擎资源")
                self._fund_ocr = None
                self._optimized_ocr = None
                
        except Exception as e:
            self.logger.error(f"清理OCR引擎资源失败: {str(e)}")


# 全局单例实例
ocr_manager = OCRManager()


def get_global_ocr_manager() -> OCRManager:
    """
    获取全局OCR管理器实例
    
    Returns:
        OCR管理器单例
    """
    return ocr_manager