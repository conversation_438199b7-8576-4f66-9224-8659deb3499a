# -*- coding: utf-8 -*-
"""
统一OCR组件
整合图像处理、OCR引擎管理和诊断功能，替代原来的fund_ocr.py
"""

import logging
import traceback
from typing import Dict, Any, Optional, List, Tuple
import numpy as np
from datetime import datetime

from image_processor import ImageProcessor
from ocr_engines import OCREngineManager
from ocr_diagnostics import OCRDiagnostics
from data_processor import parse_fund_value
from config import APP_CONFIG


class FundDataOCR:
    """多空资金数据OCR识别器 - 统一组件版本"""
    
    def __init__(self, use_gpu: bool = True, debug_mode: bool = False, save_debug_images: bool = False):
        """
        初始化OCR识别器
        
        Args:
            use_gpu: 是否使用GPU加速
            debug_mode: 是否启用调试模式
            save_debug_images: 是否保存调试图像
        """
        self.logger = logging.getLogger(__name__)
        self.use_gpu = use_gpu
        self.debug_mode = debug_mode
        self.save_debug_images = save_debug_images
        
        # 初始化组件
        self.image_processor = ImageProcessor(debug_mode, save_debug_images)
        self.ocr_engine_manager = OCREngineManager(use_gpu, debug_mode)
        self.diagnostics = OCRDiagnostics() if debug_mode else None
        
        # 兼容性属性
        self.debug_dir = self.image_processor.debug_dir if save_debug_images else None
        self.error_count = 0
        self.max_errors = 10
        
        self.logger.info("统一OCR组件初始化完成")
    
    def get_engine_status(self) -> Dict[str, Any]:
        """
        获取OCR引擎状态
        
        Returns:
            引擎状态信息
        """
        return {
            'available_engines': self.ocr_engine_manager.ocr_engines,
            'debug_mode': self.debug_mode,
            'save_debug_images': self.save_debug_images,
            'error_count': self.error_count,
            'max_errors': self.max_errors,
            'use_gpu': self.use_gpu
        }
    
    def reset_error_count(self):
        """重置错误计数"""
        self.error_count = 0
        self.ocr_engine_manager.error_count = 0
    
    def recognize_fund_data_in_region(self, x: int, y: int, width: int, height: int) -> Dict[str, Any]:
        """
        识别指定区域的多空资金数据
        
        Args:
            x, y: 区域左上角坐标
            width, height: 区域尺寸
            
        Returns:
            识别结果字典
        """
        try:
            if self.debug_mode:
                self.logger.debug(f"开始识别区域 ({x}, {y}, {width}, {height}) 的资金数据")
            
            # 截取屏幕区域
            screenshot = self.image_processor.capture_region(x, y, width, height)
            if screenshot is None:
                return {'error': '截图失败'}
            
            # 保存原始截图
            if self.save_debug_images:
                self.image_processor.save_debug_image(screenshot, f"original_{x}_{y}_{width}_{height}")
            
            # 预处理图像
            processed_image = self.image_processor.preprocess_image(screenshot)
            
            # 保存预处理后的图像
            if self.save_debug_images:
                self.image_processor.save_debug_image(processed_image, f"processed_{x}_{y}_{width}_{height}")
            
            # OCR识别
            ocr_results = self.ocr_engine_manager.multi_engine_ocr(processed_image)
            
            # 解析资金数据
            fund_data = self._parse_fund_data(ocr_results)
            
            if self.debug_mode:
                self.logger.debug(f"识别完成，资金数据: {fund_data}")
            
            return {
                'success': True,
                'fund_values': fund_data,
                'raw_ocr_results': ocr_results
            }
            
        except Exception as e:
            error_msg = f"OCR识别失败: {str(e)}"
            self.logger.error(error_msg)
            self.error_count += 1
            return {'error': error_msg}
    
    def _parse_fund_data(self, ocr_results: Dict[str, Any]) -> List[float]:
        """
        解析OCR结果中的资金数据
        
        Args:
            ocr_results: OCR识别结果
            
        Returns:
            资金数值列表
        """
        fund_values = []
        
        # 从各个引擎的结果中提取数值
        for engine_name, results in ocr_results.items():
            if not results:
                continue
                
            for result in results:
                if isinstance(result, dict) and 'text' in result:
                    text = result['text']
                elif isinstance(result, (list, tuple)) and len(result) >= 2:
                    text = result[1]  # PaddleOCR格式
                else:
                    text = str(result)
                
                # 解析数值
                value = parse_fund_value(text)
                if value != 0.0:  # 只添加有效数值
                    fund_values.append(value)
        
        return fund_values
    
    def test_raw_ocr_recognition(self, x: int, y: int, width: int, height: int) -> Dict[str, Any]:
        """
        测试原始OCR识别功能
        
        Args:
            x, y: 区域左上角坐标
            width, height: 区域尺寸
            
        Returns:
            测试结果
        """
        try:
            # 截取屏幕区域
            screenshot = self.image_processor.capture_region(x, y, width, height)
            if screenshot is None:
                return {'success': False, 'error': '截图失败'}
            
            # 直接进行OCR识别（不预处理）
            ocr_results = self.ocr_engine_manager.multi_engine_ocr(screenshot)
            
            return {
                'success': True,
                'results': ocr_results,
                'engine_status': self.get_engine_status()
            }
            
        except Exception as e:
            error_msg = f"原始OCR测试失败: {str(e)}"
            self.logger.error(error_msg)
            return {'success': False, 'error': error_msg}
    
    def test_fund_data_ocr_recognition(self, x: int, y: int, width: int, height: int) -> Dict[str, Any]:
        """
        测试多空资金数据OCR识别
        
        Args:
            x, y: 区域左上角坐标
            width, height: 区域尺寸
            
        Returns:
            测试结果
        """
        try:
            result = self.recognize_fund_data_in_region(x, y, width, height)
            
            if result.get('success'):
                return {
                    'success': True,
                    'fund_values': result.get('fund_values', []),
                    'raw_results': result.get('raw_ocr_results', {}),
                    'engine_status': self.get_engine_status()
                }
            else:
                return {
                    'success': False,
                    'error': result.get('error', '未知错误'),
                    'engine_status': self.get_engine_status()
                }
                
        except Exception as e:
            error_msg = f"资金数据OCR测试失败: {str(e)}"
            self.logger.error(error_msg)
            return {'success': False, 'error': error_msg}
    
    def test_preprocessing_methods(self, x: int, y: int, width: int, height: int) -> Dict[str, Any]:
        """
        测试不同的预处理方法
        
        Args:
            x, y: 区域左上角坐标
            width, height: 区域尺寸
            
        Returns:
            各种预处理方法的测试结果
        """
        try:
            # 截取屏幕区域
            screenshot = self.image_processor.capture_region(x, y, width, height)
            if screenshot is None:
                return {'error': '截图失败'}
            
            results = {}
            
            # 测试多种预处理策略
            strategies = self.image_processor.preprocess_multi_strategy(screenshot)
            
            for strategy_name, processed_image in strategies:
                try:
                    # 对每种预处理结果进行OCR识别
                    ocr_result = self.ocr_engine_manager.multi_engine_ocr(processed_image)
                    results[strategy_name] = ocr_result
                    
                    # 保存调试图像
                    if self.save_debug_images:
                        self.image_processor.save_debug_image(
                            processed_image, 
                            f"{strategy_name}_{x}_{y}_{width}_{height}"
                        )
                        
                except Exception as e:
                    results[strategy_name] = {'error': str(e)}
            
            return results
            
        except Exception as e:
            error_msg = f"预处理方法测试失败: {str(e)}"
            self.logger.error(error_msg)
            return {'error': error_msg}
    
    def visualize_capture_region(self, x: int, y: int, width: int, height: int) -> bool:
        """
        可视化截取区域
        
        Args:
            x, y: 区域左上角坐标
            width, height: 区域尺寸
            
        Returns:
            是否成功
        """
        try:
            return self.image_processor.visualize_capture_region(x, y, width, height)
        except Exception as e:
            self.logger.error(f"区域可视化失败: {str(e)}")
            return False
    
    def test_ocr_with_sample_image(self) -> Dict[str, Any]:
        """
        使用示例图像测试OCR功能
        
        Returns:
            测试结果
        """
        try:
            # 创建测试图像
            test_img = self.image_processor.create_test_image("123.45万")
            
            # 保存测试图像
            if self.save_debug_images:
                self.image_processor.save_debug_image(test_img, "test_sample")
            
            # 使用多引擎识别
            results = self.ocr_engine_manager.multi_engine_ocr(test_img)
            
            return {
                'success': True,
                'results': results,
                'engine_status': self.get_engine_status()
            }
            
        except Exception as e:
            error_msg = f"OCR测试失败: {str(e)}"
            self.logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'engine_status': self.get_engine_status()
            }


# 兼容性函数
def test_fund_ocr():
    """测试多空资金OCR识别"""
    try:
        print("=== OCR引擎测试 ===")
        
        # 创建OCR实例（启用调试模式）
        ocr = FundDataOCR(use_gpu=False, debug_mode=True, save_debug_images=True)
        
        print("OCR引擎初始化成功！")
        
        # 显示引擎状态
        status = ocr.get_engine_status()
        print(f"可用引擎: {status['available_engines']}")
        print(f"调试模式: {status['debug_mode']}")
        print(f"保存调试图像: {status['save_debug_images']}")
        
        # 测试示例图像识别
        print("\n=== 测试示例图像识别 ===")
        test_result = ocr.test_ocr_with_sample_image()
        if test_result['success']:
            print(f"测试成功！识别结果: {test_result['results']}")
        else:
            print(f"测试失败: {test_result['error']}")
        
        print("\n=== 测试完成 ===")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        print(f"错误详情: {traceback.format_exc()}")


def enhanced_test_fund_ocr():
    """增强版OCR测试，包含完整的诊断功能"""
    print("=== 增强版OCR诊断测试 ===")
    
    # 运行完整诊断
    diagnostics = OCRDiagnostics()
    report = diagnostics.run_full_diagnostics()
    
    print(report)
    
    # 测试FundDataOCR
    print("\n=== FundDataOCR专项测试 ===")
    test_fund_ocr()


if __name__ == "__main__":
    test_fund_ocr()
